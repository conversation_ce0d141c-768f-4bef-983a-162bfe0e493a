package one.srp.gensmo.ui.screens.detail.editor

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import coil3.compose.AsyncImage
import one.srp.core.analytics.events.SelectItem
import one.srp.core.analytics.types.EventActionType
import one.srp.core.analytics.types.EventItem
import one.srp.core.analytics.types.EventItemCategory
import one.srp.core.analytics.types.EventItemListName
import one.srp.core.analytics.types.EventMethod
import one.srp.core.analytics.types.EventRefer
import one.srp.core.network.model.FeedItem
import one.srp.core.network.model.HashtagItem
import one.srp.gensmo.R
import one.srp.gensmo.data.model.TaskStatus
import one.srp.gensmo.ui.components.exception.BaseError
import one.srp.gensmo.ui.components.hashtag.HashtagInputComponent
import one.srp.gensmo.ui.components.loading.BaseLoading
import one.srp.gensmo.ui.components.navigate.TopBar
import one.srp.gensmo.ui.components.preview.ZoomDialog
import one.srp.gensmo.ui.navigation.NavActions
import one.srp.gensmo.ui.screens.detail.editor._viewmodel.PostEditorViewModel
import one.srp.gensmo.ui.theme.AppThemeColors
import one.srp.gensmo.ui.theme.AppThemeTextStyle
import one.srp.gensmo.utils.metrics.MetricData
import one.srp.gensmo.utils.metrics.MetricViewModel
import one.srp.gensmo.utils.ui.rememberKeyboardVisibility
import one.srp.gensmo.viewmodel.feed.FeedDetailViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostEditorScreen(
    id: String? = null,
    type: String,
    navActions: NavActions = NavActions(),
    viewModel: FeedDetailViewModel = hiltViewModel(),
    postEditorViewModel: PostEditorViewModel = hiltViewModel(),
    refer: EventRefer = EventRefer.PostBoot,
) {
    // 如果id为空，显示错误页
    if (id == null) {
        return BaseError(onClick = { navActions.back() })
    }

    val isKeyboardVisible by rememberKeyboardVisibility()

    val searchStatus by viewModel.searchStatus.collectAsState()
    val feedItem by viewModel.searchResult.collectAsState()
    val postEditorUiState by postEditorViewModel.uiState.collectAsState()

    val metricViewModel: MetricViewModel = hiltViewModel()
    val metric = metricViewModel.compatMetric(refer)

    LaunchedEffect(Unit) {
        metric(
            SelectItem(
                itemListName = EventItemListName.Screen,
                method = EventMethod.PageView,
            )
        )
    }

    // 根据id和type获取feeditem数据
    LaunchedEffect(id, type) {
        when (type) {
            "collage" -> viewModel.getCollageDetail(id)
            "tryon" -> viewModel.getTryOnDetail(id)
            else -> viewModel.getCollageDetail(id) // 默认使用collage
        }
    }

    // 同步标题和描述到ViewModel
    LaunchedEffect(postEditorUiState.title, postEditorUiState.description) {
        // 初始化时从ViewModel获取值，避免重复设置
    }

    // 监听发布成功状态
    LaunchedEffect(postEditorUiState.isPublished) {
        if (postEditorUiState.isPublished) {
            postEditorViewModel.reset()
            navActions.back()
        }
    }

    val context = LocalContext.current

    fun onPostClick(item: FeedItem) {
        // 实现发布逻辑
        // 根据type确定feedType和documentId
        val feedType = when (type) {
            "tryon" -> "try_on"
            else -> "collage"
        }

        val documentId = when (type) {
            "tryon" -> item.tryOnTaskId ?: id
            else -> item.moodboardId.takeIf { it.isNotEmpty() } ?: id
        }

        metric(
            SelectItem(
                itemListName = EventItemListName.SubmitBtn,
                method = EventMethod.Click,
                actionType = EventActionType.Post,
                items = listOf(
                    EventItem(
                        itemCategory = if (feedType == "try_on") EventItemCategory.TryOnTask else EventItemCategory.GeneralCollage,
                        itemId = documentId,
                        itemName = item.reasoning,
                    )
                )
            )
        )

        postEditorViewModel.publishPost(feedType, documentId, onSuccess = {
            MetricData.logEventAF("af_post")

            Toast.makeText(context, "Post succeed.", Toast.LENGTH_SHORT)
                .show()
        }, onFail = {
            Toast.makeText(context, "Post failed.", Toast.LENGTH_SHORT)
                .show()
        })
    }

    Scaffold(
        topBar = {
            TopBar(transparent = true, onBack = { navActions.back() }, action = {
                if (isKeyboardVisible) {
                    Button(
                        onClick = {
                            feedItem?.let { item ->
                                onPostClick(item)
                            }
                        },
                        enabled = !postEditorUiState.isLoading,
                        shape = MaterialTheme.shapes.medium,
                    ) {
                        if (postEditorUiState.isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                        } else {
                            Text(
                                stringResource(R.string.text_post),
                                style = AppThemeTextStyle.Body16H
                            )
                        }
                    }
                }
            })
        }
    ) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            when (searchStatus) {
                TaskStatus.Fail -> {
                    BaseError(
                        onClick = { navActions.back() },
                        modifier = Modifier.padding(bottom = 64.dp)
                    )
                }

                TaskStatus.Success -> {
                    feedItem?.let { item ->
                        PostEditorContent(
                            feedItem = item,
                            title = postEditorUiState.title,
                            description = postEditorUiState.description,
                            hashtags = postEditorUiState.hashtags+listOf(HashtagItem(hashtag = "test")),
                            onTitleChange = { postEditorViewModel.updateTitle(it) },
                            onDescriptionChange = { postEditorViewModel.updateDescription(it) },
                            onHashtagsChange = { postEditorViewModel.updateHashtags(it) },
                            onPostClick = {
                                onPostClick(item)
                            },
                            isPublishing = postEditorUiState.isLoading,
                            publishError = postEditorUiState.error,
                            onClearError = { postEditorViewModel.clearError() },
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }

                else -> {
                    // TaskStatus.Idle - 初始状态，显示加载
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight(0.8f),
                        contentAlignment = Alignment.Center
                    ) {
                        BaseLoading()
                    }
                }
            }
        }
    }
}

@Composable
internal fun PostEditorContent(
    modifier: Modifier = Modifier,
    feedItem: FeedItem,
    title: String,
    description: String,
    hashtags: List<HashtagItem>,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onHashtagsChange: (List<HashtagItem>) -> Unit,
    onPostClick: () -> Unit,
    isPublishing: Boolean = false,
    publishError: String? = null,
    onClearError: () -> Unit = {},
) {
    val scrollState = rememberScrollState()
    val isKeyboardVisible by rememberKeyboardVisibility()

    Column(
        modifier = modifier
            .fillMaxSize()
            .imePadding()
            .padding(horizontal = 8.dp)
            .verticalScroll(scrollState),
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // 图片展示区域
        Card(
            modifier = Modifier
                .fillMaxWidth(0.6f)
                .padding(start = 16.dp),
            shape = MaterialTheme.shapes.medium,
            colors = CardDefaults.cardColors(
                containerColor = Color.Gray.copy(alpha = 0.1f)
            )
        ) {
            // 显示moodboard图片或封面图片
            val imageUrl = feedItem.tryOnCoverImage ?: feedItem.tryOnUrl ?: feedItem.image
            ?: feedItem.imageUrl

            if (imageUrl != null) {
                ZoomDialog {
                    AsyncImage(
                        model = imageUrl,
                        contentDescription = null,
                        modifier = Modifier.fillMaxWidth(),
                        contentScale = ContentScale.FillWidth,
                    )
                }
            }
        }

        // 标题输入框
        TextField(
            value = title,
            onValueChange = onTitleChange,
            placeholder = {
                Text(
                    "Add a title", style = AppThemeTextStyle.Body16H.copy(
                        AppThemeColors.Gray500
                    )
                )
            },
            modifier = Modifier.fillMaxWidth(),
            minLines = 1,
            maxLines = 2,
            textStyle = AppThemeTextStyle.Body16H,
            colors = TextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent
            )
        )

        // 描述输入框
        TextField(
            value = description,
            onValueChange = onDescriptionChange,
            placeholder = {
                Text(
                    "Tell people about your creation...",
                    style = AppThemeTextStyle.Body16H.copy(AppThemeColors.Gray500)
                )
            },
            modifier = Modifier.fillMaxWidth(),
            minLines = 1,
            maxLines = 6,
            textStyle = AppThemeTextStyle.Body16H,
            colors = TextFieldDefaults.colors(
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent
            )
        )

        // 标签输入组件
        HashtagInputComponent(
            selectedHashtags = hashtags,
            onHashtagsChanged = onHashtagsChange,
            modifier = Modifier.fillMaxWidth().padding(horizontal = 16.dp),
        )

        // 错误提示
        publishError?.let { error ->
            Text(
                text = error,
                color = MaterialTheme.colorScheme.error,
                style = AppThemeTextStyle.Body14H,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // 底部Post按钮 - 键盘可见时隐藏
        AnimatedVisibility(
            visible = !isKeyboardVisible,
            enter = slideInVertically(
                initialOffsetY = { it }
            ) + fadeIn(),
            exit = slideOutVertically(
                targetOffsetY = { it }
            ) + fadeOut()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Button(
                    onClick = {
                        if (publishError != null) {
                            onClearError()
                        }
                        onPostClick()
                    },
                    enabled = !isPublishing,
                    shape = MaterialTheme.shapes.medium,
                    modifier = Modifier.weight(1f)
                ) {
                    if (isPublishing) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    } else {
                        Text(
                            stringResource(R.string.text_post),
                            style = AppThemeTextStyle.Body16H
                        )
                    }
                }
            }
        }
    }
}
